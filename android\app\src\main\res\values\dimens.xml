<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--
Refer to App Widget Documentation for margin information
http://developer.android.com/guide/topics/appwidgets/index.html#CreatingLayout
    -->
    <dimen name="widget_margin">0dp</dimen>

    <!-- Widget dimensions -->
    <dimen name="widget_padding">16dp</dimen>
    <dimen name="widget_corner_radius">16dp</dimen>

    <!-- Header dimensions -->
    <dimen name="header_margin_bottom">12dp</dimen>
    <dimen name="title_text_size">16sp</dimen>
    <dimen name="button_size">32dp</dimen>
    <dimen name="button_margin">8dp</dimen>

    <!-- Objective item dimensions -->
    <dimen name="objective_item_padding_vertical">8dp</dimen>
    <dimen name="objective_item_padding_horizontal">12dp</dimen>
    <dimen name="objective_item_margin_bottom">6dp</dimen>
    <dimen name="objective_checkbox_size">24dp</dimen>
    <dimen name="objective_checkbox_margin">12dp</dimen>

    <!-- Text sizes -->
    <dimen name="objective_title_text_size">14sp</dimen>
    <dimen name="objective_streak_text_size">12sp</dimen>
    <dimen name="empty_message_text_size">14sp</dimen>

    <!-- Spacing -->
    <dimen name="small_spacing">4dp</dimen>
    <dimen name="medium_spacing">8dp</dimen>
    <dimen name="large_spacing">16dp</dimen>

</resources>